import gameConfig from "../../config/gameConfig.js";
import * as AchievementService from "../../core/achievement.service.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as DailyQuestHelper from "./dailyquest.helpers.js";
import * as DailyQuestRepository from "../../repositories/dailyquest.repository.js";
import { logAction } from "../../lib/actionLogger.js";
import { getToday, isSameDay } from "../../utils/dateHelpers.js";
import { LogErrorStack } from "../../utils/log.js";
import { QuestProgressStatus } from "@prisma/client";
import { UTCDate } from "@date-fns/utc";
import * as UserRepository from "../../repositories/user.repository.js";
import * as ItemRepository from "../../repositories/item.repository.js";


export const GetDailyQuestList = async (userId: number) => {
    try {
        const today = getToday();
        const dailyQuests = await DailyQuestRepository.findDailyQuestsByUserId(userId, today);
        return { data: dailyQuests };
    } catch (error) {
        LogErrorStack({ message: "Failed to get daily quests:", error });
        return { error: "Unexpected server error", statusCode: 500 };
    }
};

export const CompleteDailyQuest = async (userId: number, questId: number) => {
    try {
        const dailyQuest = await DailyQuestRepository.findDailyQuestById(questId);
        const currentUser = await UserRepository.getUserById(userId);

        if (!dailyQuest) {
            return { error: "Invalid daily task", statusCode: 400 };
        }
        if (!currentUser || dailyQuest.userId !== currentUser.id) {
            return { error: "Task is not yours", statusCode: 400 };
        }
        if (dailyQuest.questStatus !== QuestProgressStatus.ready_to_complete) {
            return { error: "Task is not complete", statusCode: 400 };
        }

        const today = getToday();
        const questDate = dailyQuest.createdAt;

        if (!isSameDay(questDate, today)) {
            return { error: "Daily task has expired", statusCode: 400 };
        }

        await DailyQuestRepository.updateDailyQuest(dailyQuest.id, { questStatus: QuestProgressStatus.complete });

        // ApplyQuestCompletion handles saving user updates internally
        await DailyQuestHelper.ApplyDailyQuestCompletion(dailyQuest, currentUser);
        await AchievementService.UpdateUserAchievement(currentUser.id, "dailyQuestsCompleted");

        logAction({
            action: "DAILY_QUEST_COMPLETED",
            userId: userId,
            info: {
                questId: dailyQuest.id,
            },
        });

        return { data: "Daily task completed" };
    } catch (error) {
        LogErrorStack({ message: "Failed to complete daily quest:", error });
        return { error: "Unexpected server error", statusCode: 500 };
    }
};

export const ClaimDailyCompletionReward = async (userId: number) => {
    try {
        const today = getToday();
        const dailyQuests = await DailyQuestRepository.countCompletedDailyQuests(userId, today);

        if (!dailyQuests || dailyQuests < 3) {
            return { error: "Daily quests not completed!", statusCode: 400 };
        }

        const item = await ItemRepository.findItemById(gameConfig.DAILY_CHEST_ITEM_ID);

        if (!item) {
            return { error: "Reward not found", statusCode: 400 };
        }

        const currentUser = await UserRepository.getUserById(userId);

        if (!currentUser) {
            return { error: "User not found", statusCode: 400 };
        }

        if (
            "dailyQuestsRewardClaimed" in currentUser &&
            currentUser.dailyQuestsRewardClaimed &&
            isSameDay(new UTCDate(currentUser.dailyQuestsRewardClaimed), today)
        ) {
            return { error: "Already claimed", statusCode: 400 };
        }

        await UserService.updateUser(currentUser.id, {
            dailyQuestsRewardClaimed: today,
        });

        await InventoryService.AddItemToUser({
            userId,
            itemId: DAILY_CHEST_ITEM_ID,
            amount: 1,
            isTradeable: true,
        });

        return { data: item };
    } catch (error) {
        LogErrorStack({ message: "Failed to get daily quests:", error });
        return { error: "Unexpected server error", statusCode: 500 };
    }
};
